import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, input, model, output, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { filter, lastValueFrom } from 'rxjs';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';
import { CarouselAspectRatio, FileFormat, MediaType, PublicationType } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { MediaPickerModalComponent } from ':modules/media/media-picker-modal/media-picker-modal.component';
import { MediaPickerFilter } from ':modules/media/media-picker-modal/media-picker-modal.interface';
import { MediaService } from ':modules/media/media.service';
import { PostMediaListComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/components/post-media-list/post-media-list.component';
import { ReelMediaComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/components/reel-media/reel-media.component';
import { MediaUploaderService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/media-uploader.service';
import { UpsertSocialPostContext } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/contexts/upsert-social-post.context';
import { IUpsertSocialPost } from ':modules/posts-v2/social-posts/models/upsert-social-post';
import { AddMediaComponent } from ':shared/components/posts-v2/medias/add-media/add-media.component';
import { IMAGE_MIME_TYPES, VIDEO_MIME_TYPES } from ':shared/components/posts-v2/medias/posts-v2-media.interface';
import { Media } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { BodyDragAndDropEventsService } from ':shared/services/body-drag-and-drop-events.service';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

const MAX_POST_MEDIA_COUNT = 10;
const MAX_REEL_MEDIA_COUNT = 1;

@Component({
    selector: 'app-social-post-medias',
    templateUrl: './social-post-medias.component.html',
    imports: [PostMediaListComponent, MatIcon, TranslateModule, NgTemplateOutlet, AddMediaComponent, ReelMediaComponent],
    providers: [MediaUploaderService],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialPostMediasComponent {
    private readonly _mediaUploaderService = inject(MediaUploaderService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _mediaService = inject(MediaService);
    private readonly _bodyDragAndDropEventsService = inject(BodyDragAndDropEventsService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _upsertSocialPostContext = inject(UpsertSocialPostContext);

    readonly medias = model.required<IUpsertSocialPost['medias']>();
    readonly publicationType = input.required<PublicationType>();
    readonly options = input<{ post?: { showEditMediaButton: boolean } }>({});
    readonly isReadonly = input<boolean>(false);
    readonly postMediaClicked = output<string>();
    readonly uploadedMedia = output<GetMediaForEditionResponseDto>();

    readonly PublicationType = PublicationType;
    readonly SvgIcon = SvgIcon;

    readonly maxMediaSizeInMo = signal(50);
    readonly mediaFormatAccepted = signal([FileFormat.PNG, FileFormat.JPEG]);
    readonly isDragging = signal(false);
    readonly multipleMediaSelection = computed(() => this.publicationType() === PublicationType.POST);
    readonly reelMedia = computed(() => {
        if (this.publicationType() !== PublicationType.REEL) {
            return undefined;
        }
        const firstMedia = this.medias()[0];
        if (!firstMedia) {
            return undefined;
        }
        if (this.medias().length >= 2 || firstMedia.uploadedMedia.type !== MediaType.VIDEO) {
            return undefined;
        }
        return firstMedia;
    });

    // This list is given to the <input type='file'>, remember that it's only a suggestion, the user can bypass this.
    readonly acceptAttribute = computed(() => {
        if (this.publicationType() === PublicationType.POST) {
            return [...IMAGE_MIME_TYPES, ...VIDEO_MIME_TYPES];
        }
        if (this.shouldHandleVideoOnly()) {
            return VIDEO_MIME_TYPES;
        }
    });

    readonly shouldHandleVideoOnly = computed(() => this.publicationType() === PublicationType.REEL);
    readonly uploadingMediaCount = computed(() => this._mediaUploaderService.mediaCount());
    readonly carouselAspectRatio = computed(() => this._upsertSocialPostContext.upsertSocialPostState.post().carouselAspectRatio);

    constructor() {
        this._handleBodyDragEvents();
        this._autoRepairMediasIfUnwantedTypeOrNumberWhenCreatingReel();

        effect(() => {
            const uploadingMediaCount = this.uploadingMediaCount();
            this._upsertSocialPostContext.trackUploadMediaCount('SocialPostMediasComponent', uploadingMediaCount);
        });
    }

    private _autoRepairMediasIfUnwantedTypeOrNumberWhenCreatingReel(): void {
        effect(() => {
            const medias = this.medias();
            if (this.publicationType() !== PublicationType.REEL) {
                return undefined;
            }
            const firstMediaType = medias[0]?.uploadedMedia.type;
            if (medias.length >= 2 || (!!firstMediaType && firstMediaType !== MediaType.VIDEO)) {
                this.medias.set([]);
            }
        });
    }

    private async _onMediaUploaded(media: GetMediaForEditionResponseDto): Promise<void> {
        this.medias.update((medias) => [...medias, { uploadedMedia: media }]);
        this.uploadedMedia.emit(media);
    }

    // ------- Events handlers : drag and drop ------- //

    private _handleBodyDragEvents(): void {
        this._bodyDragAndDropEventsService.dragEnter.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragEnter);
        this._bodyDragAndDropEventsService.dragOver.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragOver);
        this._bodyDragAndDropEventsService.dragLeave.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragLeave);
        this._bodyDragAndDropEventsService.drop.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDrop);
    }

    private _hasFile(event: DragEvent): boolean {
        return (event.dataTransfer?.types ?? []).includes('Files');
    }

    private _onDragEnter = (): void => {
        this.isDragging.set(true);
    };

    private _onDragOver = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
    };

    private _onDragLeave = (event: DragEvent): void => {
        event.preventDefault();
        this.isDragging.set(false);
    };

    private _onDrop = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
        this.isDragging.set(false);
        for (const file of Array.from(event.dataTransfer?.files ?? [])) {
            this._importMediaFromFile(file);
        }
    };

    // ------- Events handlers : upload from file / gallery ------- //

    onImportFromFile(event: Event): void {
        const target = event.target as HTMLInputElement;
        const files = target.files as FileList;
        for (const file of Array.from(files)) {
            this._importMediaFromFile(file);
        }
    }

    private async _importMediaFromFile(file: File): Promise<void> {
        const hasFreeSlots = this._hasFreeMediaSlots();
        if (!hasFreeSlots) {
            this._toastService.openWarnToast(this._translateService.instant('social_post_medias.max_medias_error'));
            return;
        }
        const res = await this._mediaUploaderService.uploadFromFile(file);
        if (!res) {
            return;
        }
        const media = await lastValueFrom(this._mediaService.getMediaForEdition({ mediaId: res.mediaId }));
        if (this.shouldHandleVideoOnly() && media?.type !== MediaType.VIDEO) {
            this._toastService.openErrorToast(this._translateService.instant('social_post_medias.media_type_error_for_reel'));
            return;
        }
        if (media) {
            this._onMediaUploaded(media);
        }
    }

    onImportMediaFromGallery(): void {
        const mediaPickerFilter = this._getMediaFilterType();
        this._customDialogService
            .open(MediaPickerModalComponent, {
                width: '600px',
                data: {
                    restaurant: this._restaurantsService.currentRestaurant,
                    multi: this.multipleMediaSelection(),
                    filter: mediaPickerFilter,
                    selectedMedias: [],
                    maxMedia: this._getMaxMediaCount(),
                },
            })
            .afterClosed()
            .subscribe({
                next: (medias: Media[] | false) => {
                    if (medias) {
                        medias.forEach((media) => {
                            this._importMediaFromGallery(media.id);
                        });
                    }
                },
                error: (err) => {
                    console.warn('err :>>', err);
                },
            });
    }

    private _getMediaFilterType(): MediaPickerFilter {
        if (this.shouldHandleVideoOnly()) {
            return MediaPickerFilter.ONLY_VIDEO;
        }
        return MediaPickerFilter.ALL;
    }

    private async _importMediaFromGallery(mediaId: string): Promise<void> {
        const hasFreeSlots = this._hasFreeMediaSlots();
        if (!hasFreeSlots) {
            this._toastService.openWarnToast(this._translateService.instant('social_post_medias.max_medias_error'));
            return;
        }
        const res = await this._mediaUploaderService.uploadFromGalleryMediaId(mediaId);
        if (!res) {
            return;
        }
        const media = await lastValueFrom(this._mediaService.getMediaForEdition({ mediaId: res.mediaId }));
        if (this.shouldHandleVideoOnly() && media?.type !== MediaType.VIDEO) {
            this._toastService.openErrorToast(this._translateService.instant('social_post_medias.media_type_error_for_reel'));
            return;
        }
        if (media) {
            this._onMediaUploaded(media);
        }
    }

    private _hasFreeMediaSlots(): boolean {
        const freeSlots = this._getMaxMediaCount() - this.medias().length - this.uploadingMediaCount();
        return freeSlots > 0;
    }

    // ------- MISC ------- //

    onDeleteReelMedia(): void {
        this.medias.set([]);
    }

    private _getMaxMediaCount(): number {
        if (this.publicationType() === PublicationType.POST) {
            return MAX_POST_MEDIA_COUNT;
        }
        if (this.publicationType() === PublicationType.REEL) {
            return MAX_REEL_MEDIA_COUNT;
        }
        return 1;
    }

    onCarouselAspectRatioChanged(aspectRatio: CarouselAspectRatio): void {
        this._upsertSocialPostContext.updateCarouselAspectRatio(aspectRatio);
    }
}
