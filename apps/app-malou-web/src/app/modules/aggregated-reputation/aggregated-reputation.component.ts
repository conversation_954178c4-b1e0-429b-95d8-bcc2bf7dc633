import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Store } from '@ngrx/store';

import * as ReviewsActions from ':modules/reviews/store/reviews.actions';

import { ReviewsDisplayMode } from '../reviews/store/reviews.reducer';

@Component({
    selector: 'app-aggregated-reputation',
    templateUrl: './aggregated-reputation.component.html',
    imports: [RouterOutlet],
})
export class AggregatedReputationComponent implements OnDestroy, OnInit {
    constructor(private readonly _store: Store) {}

    ngOnInit(): void {
        this._store.dispatch(ReviewsActions.editReviewsFiltersCurrentView({ currentView: ReviewsDisplayMode.AGGREGATED_RESTAURANTS }));
    }

    ngOnDestroy(): void {
        this._store.dispatch(ReviewsActions.resetReviewsStateExceptFilters());
    }
}
