import { ChangeDetectionStrategy, Component, computed, effect, input, signal, untracked } from '@angular/core';

import { SvgIcon } from ':shared/modules/svg-icon.enum';

import { SidenavContentRouteComponent, SidenavContentRouteOptions } from './sidenav-content-route/sidenav-content-route.component';

@Component({
    selector: 'app-sidenav-content-route-group',
    templateUrl: './sidenav-content-route-group.component.html',
    imports: [SidenavContentRouteComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SidenavContentRouteGroupComponent {
    readonly options = input.required<SidenavContentRouteOptions>();
    readonly childrenOptions = input<SidenavContentRouteOptions[]>([]);

    readonly isChildActiveByRouterLink = signal<Record<string, boolean>>({});
    readonly isAtLeastOneActiveChildren = computed(() => Object.values(this.isChildActiveByRouterLink()).some((e) => e));

    readonly isOpen = signal(false);

    readonly optionsComputed = computed<SidenavContentRouteOptions>(() => ({
        ...this.options(),
        isRouterLinkActiveOverride: this.isAtLeastOneActiveChildren() || this.options().isRouterLinkActiveOverride,
        hideBackgroundWhenActive: true,
        rightPart: {
            notificationCount: this.childrenOptions()
                .map((options) => options.rightPart?.notificationCount ?? 0)
                .reduce((acc, cur) => acc + cur, 0),
            showNotificationCount: this.isOpen(),
            svgIconOnHover: this.isOpen() ? undefined : SvgIcon.PLUS,
            showChip: this.isOpen() ? false : this.childrenOptions().some((options) => options.rightPart?.showChip),
            chipText: this.isOpen()
                ? undefined
                : this.childrenOptions()
                      .map((options) => options.rightPart?.chipText)
                      .find((text) => text !== undefined),
        },
    }));

    constructor() {
        effect(() => {
            const oldIsChildActiveByRouterLink = untracked(() => this.isChildActiveByRouterLink());
            const newIsChildActiveByRouterLink = this.childrenOptions().reduce((acc, cur) => {
                const key = this._computeRouterLinkToKey(cur.routerLink);
                return {
                    ...acc,
                    [key]: oldIsChildActiveByRouterLink[key] ?? cur,
                };
            }, {});
            this.isChildActiveByRouterLink.set(newIsChildActiveByRouterLink);
        });
        effect(() => {
            this.isOpen.set(this.isAtLeastOneActiveChildren());
        });
    }

    onIsActive(value: boolean, routerLink: string[]): void {
        this.isChildActiveByRouterLink.update((isChildActiveByRouterLink) => {
            isChildActiveByRouterLink[this._computeRouterLinkToKey(routerLink)] = value;
            return { ...isChildActiveByRouterLink };
        });
    }

    private _computeRouterLinkToKey(routerLink: string[]): string {
        return routerLink.join('/');
    }
}
