@let base = ['/restaurants', selectedRestaurant()?._id ?? ''];
<app-sidenav-content-route-group
    [options]="{
        routerLink: base | concat: ['interactions'],
        leftPart: {
            text: 'sidenav_content.interactions' | translate,
            colorClassWhenActive: 'malou-color-text-1',
            svgIcon: SvgIcon.CONVERSATION,
            svgIconSize: 'medium',
        },
    }"
    [childrenOptions]="[
        {
            routerLink: base | concat: ['interactions', 'messages'],
            leftPart: {
                text: 'sidenav_content.messages' | translate,
                colorClassWhenActive: 'malou-color-primary',
                svgIcon: SvgIcon.DOT,
                svgIconSize: 'small',
                hideIconWhenActive: true,
            },
            rightPart: {
                notificationCount: unreadConversationCount(),
            },
        },
        {
            routerLink: base | concat: ['interactions', 'comments'],
            leftPart: {
                text: 'sidenav_content.comments' | translate,
                colorClassWhenActive: 'malou-color-primary',
                svgIcon: SvgIcon.DOT,
                svgIconSize: 'small',
                hideIconWhenActive: true,
            },
            rightPart: {
                notificationCount: unreadCommentsCount() + unreadMentionsCount(),
            },
        },
    ]" />
