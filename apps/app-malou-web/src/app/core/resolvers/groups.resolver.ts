import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { filter, take } from 'rxjs';

import { isNotNil } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { initializeAggregatedFilters } from ':core/user-filters/store/user-filters.actions';
import * as PlatformsActions from ':modules/platforms/store/platforms.actions';
import { selectUserInfos } from ':modules/user/store/user.selectors';

@Injectable()
export class GroupsResolver {
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _store = inject(Store);

    resolve(): void {
        this._restaurantsService.setSelectedRestaurant(null);
        this._store.dispatch(PlatformsActions.editSelectedRestaurantId({ restaurantId: '' }));
        this._initAggregatedReviewsState();
    }

    private _initAggregatedReviewsState(): void {
        this._store
            .select(selectUserInfos)
            .pipe(filter(isNotNil), take(1))
            .subscribe((userInfos) => {
                this._store.dispatch({ type: initializeAggregatedFilters.type, userId: userInfos._id });
            });
    }
}
