import { DateTime } from 'luxon';
import { container } from 'tsyringe';
import { v4 as uuid } from 'uuid';

import { AggregatedSocialPostInsightDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { MalouMetric, PlatformKey, PostInsightEntityType, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatformInsight } from ':modules/platform-insights/tests/platform-insight.builder';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultPostInsight } from ':modules/post-insights/v2/tests/post-insights.builder';
import { GetAggregatedSocialPostInsightsUseCase } from ':modules/post-insights/v2/use-cases/get-aggregated-social-post-insights/get-aggregated-social-post-insights.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('GetAggregatedSocialPostInsightsUseCase', () => {
    beforeEach(async () => {
        registerRepositories(['RestaurantsRepository', 'PlatformsRepository', 'PostInsightRepository', 'PlatformInsightsRepository']);
    });

    describe('execute', () => {
        it.only('should return aggregated social post insights', async () => {
            // Arrange
            // restaurants ids
            const restaurantId_1 = newDbId();
            const restaurantId_2 = newDbId();
            // platform social ids
            const fbPlatformSocialId_1 = 'fb_social_id_1';
            const igPlatformSocialId_1 = 'ig_social_id_1';
            const tiktokPlatformSocialId_1 = 'tiktok_social_id_1';
            const fbPlatformSocialId_2 = 'fb_social_id_2';
            const igPlatformSocialId_2 = 'ig_social_id_2';
            const tiktokPlatformSocialId_2 = 'tiktok_social_id_2';

            const tenDaysAgoDT = DateTime.now().minus({ days: 10 });
            const lastMonthPeriod = {
                startDate: DateTime.now().minus({ months: 1 }).toJSDate(),
                endDate: DateTime.now().toJSDate(),
            };
            const requestBody = {
                restaurantIds: [restaurantId_1.toString(), restaurantId_2.toString()],
                startDate: lastMonthPeriod.startDate,
                endDate: lastMonthPeriod.endDate,
                platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK],
                previousPeriod: false,
            };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'postInsights' | 'platformInsights'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().internalName('restaurant1_IN')._id(restaurantId_1).build(),
                                getDefaultRestaurant().internalName('restaurant2_IN')._id(restaurantId_2).build(),
                            ];
                        },
                    },
                    platforms: {
                        data() {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_1)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialId(fbPlatformSocialId_1)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_1)
                                    .key(PlatformKey.INSTAGRAM)
                                    .socialId(igPlatformSocialId_1)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_1)
                                    .key(PlatformKey.TIKTOK)
                                    .socialId(tiktokPlatformSocialId_1)
                                    .build(),

                                getDefaultPlatform()
                                    .restaurantId(restaurantId_2)
                                    .key(PlatformKey.FACEBOOK)
                                    .socialId(fbPlatformSocialId_2)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_2)
                                    .key(PlatformKey.INSTAGRAM)
                                    .socialId(igPlatformSocialId_2)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(restaurantId_2)
                                    .key(PlatformKey.TIKTOK)
                                    .socialId(tiktokPlatformSocialId_2)
                                    .build(),
                            ];
                        },
                    },
                    postInsights: {
                        data() {
                            return [
                                // instagram post insights (restaurant 1)
                                getDefaultPostInsight()
                                    .platformSocialId(igPlatformSocialId_1)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .entityType(PostInsightEntityType.POST)
                                    .socialId(uuid())
                                    .postSocialCreatedAt(tenDaysAgoDT.toJSDate())
                                    .data({
                                        impressions: 10,
                                        likes: 10,
                                        comments: 10,
                                        shares: 10,
                                        saved: 10,
                                        plays: null,
                                        reach: null,
                                        totalInteractions: 100,
                                    })
                                    .build(),
                                // tiktok post insights (restaurant 1)
                                getDefaultPostInsight()
                                    .platformSocialId(tiktokPlatformSocialId_1)
                                    .platformKey(PlatformKey.TIKTOK)
                                    .postSocialCreatedAt(tenDaysAgoDT.toJSDate())
                                    .socialId(uuid())
                                    .data({ impressions: 20, likes: 20, comments: 20, shares: 20, saved: 20, plays: null, reach: null })
                                    .build(),
                                // instagram post insights (restaurant 2)
                                getDefaultPostInsight()
                                    .platformSocialId(igPlatformSocialId_2)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .entityType(PostInsightEntityType.REEL)
                                    .postSocialCreatedAt(tenDaysAgoDT.toJSDate())
                                    .socialId(uuid())
                                    .data({
                                        impressions: 30,
                                        likes: 30,
                                        comments: 30,
                                        shares: 30,
                                        saved: 30,
                                        plays: 30,
                                        reach: null,
                                        totalInteractions: 300,
                                    })
                                    .build(),
                                getDefaultPostInsight()
                                    .platformSocialId(igPlatformSocialId_2)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .entityType(PostInsightEntityType.REEL)
                                    .postSocialCreatedAt(tenDaysAgoDT.toJSDate())
                                    .socialId(uuid())
                                    .data({
                                        impressions: 35,
                                        likes: 35,
                                        comments: 35,
                                        shares: 35,
                                        saved: 35,
                                        plays: 35,
                                        reach: null,
                                        totalInteractions: 350,
                                    })
                                    .build(),
                                // tiktok post insights (restaurant 2)
                                getDefaultPostInsight()
                                    .platformSocialId(tiktokPlatformSocialId_2)
                                    .platformKey(PlatformKey.TIKTOK)
                                    .postSocialCreatedAt(tenDaysAgoDT.toJSDate())
                                    .socialId(uuid())
                                    .data({ impressions: 40, likes: 40, comments: 40, shares: 40, saved: 40, plays: null, reach: null })
                                    .build(),
                            ];
                        },
                    },
                    platformInsights: {
                        data() {
                            return [
                                // facebook platform insights (restaurant 1)
                                getDefaultPlatformInsight()
                                    .socialId(fbPlatformSocialId_1)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .metric(StoredInDBInsightsMetric.FOLLOWERS)
                                    .value(100)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .build(),
                                getDefaultPlatformInsight()
                                    .socialId(fbPlatformSocialId_1)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .metric(StoredInDBInsightsMetric.IMPRESSIONS)
                                    .value(100)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .build(),
                                getDefaultPlatformInsight()
                                    .socialId(fbPlatformSocialId_1)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .metric(StoredInDBInsightsMetric.PAGE_POST_ENGAGEMENTS)
                                    .value(100)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .build(),
                                // facebook platform insights (restaurant 2)
                                getDefaultPlatformInsight()
                                    .socialId(fbPlatformSocialId_2)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .metric(StoredInDBInsightsMetric.FOLLOWERS)
                                    .value(200)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .build(),
                                getDefaultPlatformInsight()
                                    .socialId(fbPlatformSocialId_2)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .metric(StoredInDBInsightsMetric.IMPRESSIONS)
                                    .value(200)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .build(),
                                getDefaultPlatformInsight()
                                    .socialId(fbPlatformSocialId_2)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .metric(StoredInDBInsightsMetric.PAGE_POST_ENGAGEMENTS)
                                    .value(200)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .build(),

                                // instagram platform insights (restaurant 1)
                                getDefaultPlatformInsight()
                                    .socialId(igPlatformSocialId_1)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .metric(StoredInDBInsightsMetric.FOLLOWERS)
                                    .value(100)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .day(tenDaysAgoDT.day)
                                    .month(tenDaysAgoDT.month - 1)
                                    .year(tenDaysAgoDT.year)
                                    .build(),
                                getDefaultPlatformInsight()
                                    .socialId(igPlatformSocialId_1)
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .metric(StoredInDBInsightsMetric.FOLLOWERS)
                                    .value(200)
                                    .day(DateTime.now().minus({ days: 2 }).day)
                                    .month(DateTime.now().minus({ days: 2 }).month - 1)
                                    .year(DateTime.now().minus({ days: 2 }).year)
                                    .date(DateTime.now().minus({ days: 2 }).toJSDate())
                                    .build(),

                                // tiktok platform insights (restaurant 1)
                                getDefaultPlatformInsight()
                                    .socialId(tiktokPlatformSocialId_1)
                                    .platformKey(PlatformKey.TIKTOK)
                                    .metric(StoredInDBInsightsMetric.FOLLOWERS)
                                    .value(100)
                                    .date(tenDaysAgoDT.toJSDate())
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): AggregatedSocialPostInsightDto => {
                    return {
                        [restaurantId_1.toString()]: {
                            [PlatformKey.FACEBOOK]: {
                                [MalouMetric.IMPRESSIONS]: 100,
                                [MalouMetric.ENGAGEMENTS]: 100,
                                [MalouMetric.POSTS]: 0, // No posts data for Facebook
                                [MalouMetric.FOLLOWERS]: 100,
                            },
                            [PlatformKey.INSTAGRAM]: {
                                [MalouMetric.IMPRESSIONS]: 10,
                                [MalouMetric.ENGAGEMENTS]: 100,
                                [MalouMetric.POSTS]: 1,
                                [MalouMetric.FOLLOWERS]: 200,
                            },
                            [PlatformKey.TIKTOK]: {
                                [MalouMetric.IMPRESSIONS]: 20,
                                [MalouMetric.ENGAGEMENTS]: 60,
                                [MalouMetric.POSTS]: 1,
                                [MalouMetric.FOLLOWERS]: 100,
                            },
                        },
                        [restaurantId_2.toString()]: {
                            [PlatformKey.FACEBOOK]: {
                                [MalouMetric.IMPRESSIONS]: 200,
                                [MalouMetric.ENGAGEMENTS]: 200,
                                [MalouMetric.POSTS]: 0, // No posts data for Facebook
                                [MalouMetric.FOLLOWERS]: 200,
                            },
                            [PlatformKey.INSTAGRAM]: {
                                [MalouMetric.IMPRESSIONS]: 65,
                                [MalouMetric.ENGAGEMENTS]: 650,
                                [MalouMetric.POSTS]: 2,
                                [MalouMetric.FOLLOWERS]: null, // No followers data for Instagram
                            },
                            [PlatformKey.TIKTOK]: {
                                [MalouMetric.IMPRESSIONS]: 40,
                                [MalouMetric.ENGAGEMENTS]: 120,
                                [MalouMetric.POSTS]: 1,
                                [MalouMetric.FOLLOWERS]: null, // No followers data for TikTok
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const useCase = container.resolve(GetAggregatedSocialPostInsightsUseCase);

            const expectedResult = testCase.getExpectedResult();
            const result = await useCase.execute(requestBody);

            expect(result).toEqual(expectedResult);
        });
    });
});
