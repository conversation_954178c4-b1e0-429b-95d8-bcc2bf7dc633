import { PubSub } from '@google-cloud/pubsub';
import { container } from 'tsyringe';

import { IRestaurant, IReview, newDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { PUBSUB_STARTUP_COLOR } from ':agenda-jobs/logs-colors';
import { Config } from ':config';
import { AsyncLocalStorageService } from ':helpers/classes/async-local-storage-service';
import { MalouError } from ':helpers/classes/malou-error';
import { getColoredString } from ':helpers/get-colored-string';
import { logger } from ':helpers/logger';
import { DEFAULT_REVIEWER_NAME_VALIDATION } from ':microservices/ai-previous-review-analysis.service';
import { IntelligentSubjectsDetectionService } from ':modules/ai/services/intelligent-subjects-detection/intelligent-subjects-detection.service';
import { AutoReplyUseCases } from ':modules/automations/auto-reply.use-cases';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import { CreateNewReviewsNotificationProducer } from ':modules/notifications/queues/create-new-reviews-notification/create-new-reviews-notification.producer';
import { PlatformUpdateSuggestionNotificationService } from ':modules/notifications/services/platform-update-suggestion-service/platform-update-suggestion-notification.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GmbReviewMapper } from ':modules/reviews/platforms/gmb/gmb-review-mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import ReviewsUseCases from ':modules/reviews/reviews.use-cases';
import { GenerateKeywordAnalysisForCommentService } from ':modules/reviews/services/generate-keyword-analysis-for-comment.service';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { StartReviewSemanticAnalysisService } from ':modules/segment-analyses/services/start-review-semantic-analysis.service';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

const privateKeys = Config.businessNotificationsAccountKey;

const subscriptionNameOrId = process.env.GOOGLE_REVIEWS_SUBSCRIPTION_NAME;

// Creates a client cache this for further use
const pubSubClient = new PubSub({
    projectId: privateKeys.project_id,
    credentials: { client_email: privateKeys.client_email, private_key: privateKeys.private_key?.replaceAll('||n||', '\n') },
});

// const gmbReviewsUseCases = container.resolve(GmbReviewsUseCases);
const platformsRepository = container.resolve(PlatformsRepository);
const restaurantsRepository = container.resolve(RestaurantsRepository);
const reviewsRepository = container.resolve(ReviewsRepository);
const gmbApiProviderUseCases = container.resolve(GmbApiProviderUseCases);
const reviewsUseCases = container.resolve(ReviewsUseCases);
const startReviewSemanticAnalysisService = container.resolve(StartReviewSemanticAnalysisService);
const segmentAnalysesRepository = container.resolve(SegmentAnalysesRepository);
const asyncLocalStorageService = container.resolve(AsyncLocalStorageService);
const autoReplyUseCases = container.resolve(AutoReplyUseCases);
const createNewReviewsNotificationProducer = container.resolve(CreateNewReviewsNotificationProducer);
const getKeywordAnalysisForCommentService = container.resolve(GenerateKeywordAnalysisForCommentService);
const intelligentSubjectsDetectionService = container.resolve(IntelligentSubjectsDetectionService);
const platformUpdateSuggestionNotificationService = container.resolve(PlatformUpdateSuggestionNotificationService);

export const subscribeForReviews = async () => {
    logger.info(getColoredString('[PUB_SUB_REVIEWS] - Initializing...', PUBSUB_STARTUP_COLOR));
    if (!subscriptionNameOrId) {
        logger.info('[PUB_SUB_REVIEWS] No subscription name or id provided');
        throw new MalouError(MalouErrorCode.NO_SUBSCRIPTION_NAME_OR_ID, { message: 'No subscription name or id provided' });
    }
    // References an existing subscription
    const subscription = pubSubClient.subscription(subscriptionNameOrId);

    // Create an event handler to handle messages
    const messageHandler = async (message) => {
        if (!message) {
            return;
        }
        try {
            logger.info('[PUB_SUB_REVIEWS] Received message :', {
                id: message.id,
                data: message.data?.toString(),
                attributes: message.attributes,
            });
            // "Ack" (acknowledge receipt of) the message
            message.ack();
            const data = JSON.parse(message.data.toString());
            const platform = await platformsRepository.findOne({
                filter: {
                    apiEndpointV2: `locations/${data.location.split('/')[3]}`,
                    key: PlatformKey.GMB,
                },
                options: { lean: true },
            });
            if (!platform) {
                logger.info('[PUB_SUB_REVIEWS] no platform found for this location', data.location);
                message.ack();
                return;
            }
            const restaurant = await restaurantsRepository.findOne({
                filter: { _id: platform.restaurantId },
                projection: { active: 1, name: 1 },
                options: { lean: true, populate: [{ path: 'attributeList' }] },
            });

            if (!restaurant?.active) {
                logger.info('[PUB_SUB_REVIEWS] restaurant not active', restaurant);
                message.ack();
                return;
            }
            switch (data.type) {
                case 'NEW_REVIEW':
                    logger.info('[PUB_SUB_REVIEWS] NEW_REVIEW - Inserting new review', { platformId: platform._id, review: data.review });
                    let rawReview;
                    try {
                        for (const credential of platform.credentials ?? []) {
                            rawReview = await gmbApiProviderUseCases.fetchReviewReceived(credential, data.review);
                            if (rawReview) {
                                break;
                            }
                        }
                    } catch (e) {
                        logger.error('[PUB_SUB_REVIEWS] NEW_REVIEW - Error', e);
                        return;
                    }
                    const mappedReview = GmbReviewMapper.mapToMalouReview(rawReview);
                    try {
                        const updatedReview = await reviewsRepository.upsert({
                            filter: { socialId: mappedReview.socialId, platformId: platform._id },
                            update: {
                                ...mappedReview,
                                key: PlatformKey.GMB,
                                platformId: platform._id,
                                restaurantId: platform.restaurantId,
                                comments: mappedReview.comments.map((comment) => ({
                                    ...comment,
                                    _id: newDbId(),
                                })),
                                socialSortDate: mappedReview.socialUpdatedAt ?? mappedReview.socialCreatedAt ?? undefined,
                            } as any,
                        });
                        const reviewCommentsWithKeywordAnalysis = await getCommentsWithKeywordAnalysis(updatedReview, restaurant);
                        await reviewsRepository.findOneAndUpdate({
                            filter: { _id: updatedReview._id },
                            update: { comments: reviewCommentsWithKeywordAnalysis },
                        });

                        await intelligentSubjectsDetectionService.detectIntelligentSubjectsForReviews({
                            restaurantId: platform.restaurantId.toString(),
                            reviews: [updatedReview],
                        });

                        const isNewSemanticAnalysisFeatureEnabledForRestaurant = await isFeatureAvailableForRestaurant({
                            restaurantId: platform.restaurantId.toString(),
                            featureName: 'release-new-semantic-analysis',
                        });
                        if (isNewSemanticAnalysisFeatureEnabledForRestaurant) {
                            await handleFetchSemanticAnalysis(updatedReview);
                        }
                        await reviewsUseCases.detectLangAndUpdateReviews([updatedReview], PlatformKey.GMB);
                        autoReplyUseCases.handleReviewAutoReply(updatedReview).catch((e) => {
                            logger.error('[PUB_SUB_REVIEWS] NEW_REVIEW - handleAutoReplySideEffect Error', e);
                        });
                        await sendNewReviewNotification(updatedReview);
                    } catch (e) {
                        logger.error('[PUB_SUB_REVIEWS] NEW_REVIEW - Already inserted review', e);
                    }
                    break;
                case 'UPDATED_REVIEW':
                    logger.info('[PUB_SUB_REVIEWS] UPDATED_REVIEW - updating review', { platformId: platform._id, review: data.review });
                    const platformUpdatedReview = await gmbApiProviderUseCases.fetchReviewReceived(platform.credentials![0], data.review);
                    const mappedUpdatedReview: any = GmbReviewMapper.mapToMalouReview(platformUpdatedReview);
                    const reviewToUpdate = await reviewsRepository.findOneOrFail({ filter: { socialId: platformUpdatedReview.reviewId } });
                    const hasCommentsWithoutKeywordAnalysis = reviewToUpdate?.comments?.some((comment) => !comment.keywordAnalysis);
                    if (hasCommentsWithoutKeywordAnalysis) {
                        reviewToUpdate.comments = await getCommentsWithKeywordAnalysis(reviewToUpdate, restaurant);
                    }
                    const updatedReview = await reviewsRepository.findOneAndUpdate({
                        filter: { socialId: mappedUpdatedReview.socialId, platformId: platform._id },
                        update: {
                            ...mappedUpdatedReview,
                            archived: reviewToUpdate?.archived,
                            socialAttachments: reviewToUpdate?.socialAttachments,
                            wasAnsweredAutomatically: reviewToUpdate?.wasAnsweredAutomatically,
                            comments: mappedUpdatedReview?.comments?.map((comment) => {
                                const associatedComment = reviewToUpdate?.comments?.find((c) => c.text === comment.text);
                                return {
                                    ...associatedComment,
                                    ...comment,
                                    _id: newDbId(),
                                };
                            }),
                        },
                    });

                    if (!updatedReview) {
                        logger.warn('[PUB_SUB_REVIEWS] UPDATED_REVIEW - Review not found', {
                            gmbReviewId: platformUpdatedReview.reviewId,
                            socialId: mappedUpdatedReview.socialId,
                            platformId: platform._id,
                        });
                    } else {
                        if (reviewToUpdate?.text !== updatedReview.text) {
                            await segmentAnalysesRepository.deleteMany({
                                filter: { platformKey: PlatformKey.GMB, reviewSocialId: updatedReview.socialId.toString() },
                            });

                            const isNewSemanticAnalysisFeatureEnabledForRestaurant = await isFeatureAvailableForRestaurant({
                                restaurantId: platform.restaurantId.toString(),
                                featureName: 'release-new-semantic-analysis',
                            });
                            if (isNewSemanticAnalysisFeatureEnabledForRestaurant) {
                                await handleFetchSemanticAnalysis(updatedReview);
                            }
                        }
                        await reviewsUseCases.detectLangAndUpdateReviews([updatedReview], PlatformKey.GMB);
                        autoReplyUseCases.handleReviewAutoReply(updatedReview, { shouldForceNewReply: true }).catch((e) => {
                            logger.error('[PUB_SUB_REVIEWS] UPDATED_REVIEW - handleAutoReplySideEffect Error', e);
                        });
                    }

                    break;
                case 'NEW_CUSTOMER_MEDIA':
                    logger.info('[PUB_SUB_REVIEWS] NEW_CUSTOMER_MEDIA - Inserting new review media to platform', {
                        platformId: platform._id,
                    });
                    const medias: any[] = [];
                    for (const mediaItemName of data.mediaItemNames) {
                        const media = await gmbApiProviderUseCases.fetchMediaReceived(platform.credentials![0], mediaItemName);
                        medias.push(media);
                    }
                    logger.info('[PUB_SUB_REVIEWS] NEW_CUSTOMER_MEDIA - medias', medias);
                    // const mappedMedias = gmbReviewsUseCases.buildMediaItemsMap(medias);
                    // const profilePhotoUrl = medias[0].attribution.profilePhotoUrl;
                    // const profileName = medias[0].attribution.profileName;
                    // await reviewsRepository.findOneAndUpdate({
                    //     filter: { reviewer: { profilePhotoUrl, displayName: profileName } },
                    //     update: { socialAttachments: mappedMedias },
                    // });
                    break;
                case 'GOOGLE_UPDATE':
                    logger.info('[PUB_SUB_REVIEWS] GOOGLE_UPDATE - Google My Business update received', {
                        platformId: platform._id.toString(),
                        update: data.update,
                    });
                    await platformUpdateSuggestionNotificationService.createPlatformUpdateSuggestionNotifications({
                        platformId: platform._id.toString(),
                    });
                    break;
                default:
                    logger.info('[PUB_SUB_REVIEWS] NEW_CUSTOMER_MEDIA - Unknown message type', { messageType: data.type });
                    break;
            }
        } catch (error) {
            logger.warn('[PUB_SUB_REVIEWS] - Error', error);
        }
    };
    subscription.on('message', (...args) => asyncLocalStorageService.updateOrCreateStoreAndRun({}, messageHandler, ...args));
    logger.info(getColoredString('[PUB_SUB_REVIEWS] - Initialized', PUBSUB_STARTUP_COLOR));
};

const sendNewReviewNotification = async (review: IReview) => {
    if (!review._id) {
        return;
    }
    await createNewReviewsNotificationProducer.execute({
        reviewIds: [review._id.toString()],
    });
};

const handleFetchSemanticAnalysis = (review: IReview) => {
    if (!review.semanticAnalysisFetchStatus) {
        return startReviewSemanticAnalysisService.execute({ review }).catch((e) => {
            logger.error('[PUB_SUB_REVIEWS] UPDATED_REVIEW - startReviewSemanticAnalysisSideEffect Error', e);
        });
    }
};

const getCommentsWithKeywordAnalysis = (review: IReview, restaurant: Pick<IRestaurant, '_id' | 'name'>) =>
    Promise.all(
        review?.comments.map(async (comment) => {
            if (comment.keywordAnalysis || !comment.text) {
                return comment;
            }
            const keywordAnalysis = await getKeywordAnalysisForCommentService.execute({
                reviewId: review._id.toString(),
                restaurant,
                reviewText: review.text ?? undefined,
                rating: review.rating ?? null,
                text: comment.text,
                reviewLang: review.lang ?? '',
                reviewSocialCreatedAt: review.socialCreatedAt,
                reviewerName: review.reviewer?.displayName ?? '',
                commentSocialUpdatedAt: comment.socialUpdatedAt,
                reviewerNameValidation: review.reviewerNameValidation ?? DEFAULT_REVIEWER_NAME_VALIDATION,
            });
            return {
                ...comment,
                keywordAnalysis,
            };
        })
    );
