import { render } from '@react-email/render';
import { container } from 'tsyringe';

import {
    ConfirmCreateAccountMailTemplate,
    MalouPasswordChangeMailTemplate,
    WrongPlatformAccessMailTemplate,
} from '@malou-io/package-emails';
import { IUser } from '@malou-io/package-models';
import { getPlatformDefinition, mapApplicationLanguageToLocale, PlatformKey } from '@malou-io/package-utils';

import { MailOptions } from ':modules/mailing/email-sender.service';
import { Translation } from ':services/translation.service';

const translationService = container.resolve(Translation);

export const getResetPasswordEmail = ({
    user,
    url,
}: {
    user: Pick<IUser, 'email' | 'name' | 'defaultLanguage'>;
    url: string;
}): MailOptions => {
    const lang = mapApplicationLanguageToLocale(user.defaultLanguage);
    const translatorFunctions = translationService.fromLang({ lang }).mailing.user_email.reset_password;
    const receiver = user.name || '';
    const subject = translatorFunctions.subject();
    const html = render(MalouPasswordChangeMailTemplate({ link: url, locale: lang, receiver }));

    return {
        lang,
        to: user.email,
        subject,
        html,
    };
};

export const getConfirmCreateAccountEmail = ({
    user,
    url,
}: {
    user: Pick<IUser, 'email' | 'name' | 'defaultLanguage'>;
    url: string;
}): MailOptions => {
    const lang = mapApplicationLanguageToLocale(user.defaultLanguage);
    const translatorFunctions = translationService.fromLang({ lang }).mailing.user_email.confirm_create_account;

    const receiver = user.name || '';
    const fullUrl = `${url}?&lang=${lang}`;

    const html = render(ConfirmCreateAccountMailTemplate({ link: fullUrl, locale: lang, receiver }));

    const subject = translatorFunctions.subject();
    return {
        lang,
        to: user.email,
        subject,
        html,
    };
};

export const getWrongPlatformAccessEmail = (
    restaurantId: string,
    receivers: string,
    platformKey: PlatformKey,
    user: Pick<IUser, 'defaultLanguage' | 'name' | 'lastname'>
): MailOptions => {
    const platformName = getPlatformDefinition(platformKey)?.fullName;
    const url = `${process.env.BASE_URL}/restaurants/${restaurantId}/platforms`;

    const lang = mapApplicationLanguageToLocale(user?.defaultLanguage);
    const translatorFunctions = translationService.fromLang({ lang }).mailing.user_email.wrong_platform_access;
    const subject = translatorFunctions.subject();
    const receiver = `${user.name} ${user.lastname}`;

    const html = render(WrongPlatformAccessMailTemplate({ link: url, locale: lang, platformName, receiver }));

    return {
        lang,
        to: receivers,
        subject,
        html,
    };
};
