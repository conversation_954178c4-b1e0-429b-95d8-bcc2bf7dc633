import assert from 'node:assert/strict';
import { autoInjectable } from 'tsyringe';

import { DbId, IConversation, IMessage, IPlatform } from '@malou-io/package-models';
import { ConversationStatus, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import {
    ConversationPlatform,
    FbMessage,
    FbUser,
    IgMessage,
    IgUser,
    MessagingMessageWebhook,
    MessagingReactionWebhook,
    MessagingType,
    MetaAttachmentTypes,
    SocialConversationWithMessages,
} from ':modules/credentials/platforms/facebook/facebook.types';
import * as fbCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { ConversationsRepository, MessagesRepository } from ':modules/messages/messages.repository';
import { FacebookConversationMapper } from ':modules/messages/platforms/facebook/facebook-conversation-mapper';
import { InstagramConversationMapper } from ':modules/messages/platforms/instagram/instagram-conversation-mapper';
import { CreateMessageNotificationProducer } from ':modules/notifications/queues/create-message-notification/create-message-notification.producer';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { pusher } from ':plugins/pusher';

const isMessage = (messaging: MessagingMessageWebhook | MessagingReactionWebhook): messaging is MessagingMessageWebhook =>
    (messaging as MessagingMessageWebhook).message !== undefined;
const isFbMessage = (message: IgMessage | FbMessage): message is FbMessage => (message as FbMessage).thread_id !== undefined;

@autoInjectable()
export default class FacebookWebhookMessagesUseCases {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _conversationsRepository: ConversationsRepository,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository,
        private readonly _messagesRepository: MessagesRepository,
        private readonly _createMessageNotificationProducer: CreateMessageNotificationProducer
    ) {}

    handleMessage = async (messaging: MessagingMessageWebhook[] | MessagingReactionWebhook[], pageId: string, isFacebookPage: boolean) => {
        const webhookMessageData = messaging[0];
        const dataType: MessagingType = Object.keys(webhookMessageData).pop() as MessagingType;
        const {
            sender: { id: senderId },
            recipient: { id: recipientId },
        } = webhookMessageData;
        const customerId = pageId === senderId ? recipientId : senderId;
        const messageId = webhookMessageData?.[dataType]?.mid;
        const deleteMessage = isMessage(webhookMessageData) ? webhookMessageData?.message.is_deleted : false;

        let count = 0;
        const MAX_COUNT = 100;
        const validCredentialIdsForMessages = await this.getPlatformCredentialIds(isFacebookPage, pageId);
        let credentialId: string | undefined = undefined;
        let platformMessage: IgMessage | FbMessage | undefined = undefined;
        logger.info('[FB_WEBHOOK][WHILE_TRACKER] - handle message started', {
            messageId,
            pageId,
            isFacebookPage,
            dataType,
        });
        while (!platformMessage && count < validCredentialIdsForMessages?.length && count < MAX_COUNT) {
            try {
                credentialId = validCredentialIdsForMessages[count];
                platformMessage = await fbCredentialsUseCases.getMessage(
                    credentialId,
                    messageId,
                    pageId,
                    isFacebookPage ? ConversationPlatform.MESSENGER : ConversationPlatform.INSTAGRAM
                );
                count += 1;
            } catch (error) {
                if (deleteMessage || String(error).match(/(#9000001) This Message has been deleted/)) {
                    this.triggerMessageDeleted(messageId, isFacebookPage).catch((e) =>
                        logger.error('[FB_WEBHOOK][MESSAGE_DELETED_ERROR]', { messageId, error: e })
                    );
                    return 'ok';
                }
                logger.error('[FB_WEBHOOK][WHILE_TRACKER_ERROR][MESSAGES]', { credentialId, messageId, pageId, isFacebookPage, error });
                count += 1;
            }
        }
        logger.info('[FB_WEBHOOK][WHILE_TRACKER] - handle message ended', { messageId, pageId, platformMessage });
        if (!platformMessage) {
            logger.warn('[FB_WEBHOOK][HANDLE_MESSAGE_ERROR] - did not work', { messaging, pageId, isFacebookPage });
            return 'did not work';
        }

        let platformConversation:
            | SocialConversationWithMessages<ConversationPlatform.INSTAGRAM>
            | SocialConversationWithMessages<ConversationPlatform.MESSENGER>
            | undefined;

        assert(credentialId);
        try {
            if (isFbMessage(platformMessage)) {
                platformConversation = await fbCredentialsUseCases.getConversation(credentialId, platformMessage.thread_id, pageId);
            } else if (customerId) {
                const customerConversations = await fbCredentialsUseCases.getAllConversations(
                    credentialId,
                    pageId,
                    isFacebookPage ? ConversationPlatform.MESSENGER : ConversationPlatform.INSTAGRAM,
                    customerId
                );
                if (customerConversations?.data?.length === 1) {
                    platformConversation = customerConversations.data[0];
                }
            } else {
                logger.info('[FB_WEBHOOK_MESSAGING] Should never be called as customerId should always be defined');
                const allConversations = await fbCredentialsUseCases.getAllConversations(
                    credentialId,
                    pageId,
                    isFacebookPage ? ConversationPlatform.MESSENGER : ConversationPlatform.INSTAGRAM
                );
                platformConversation = allConversations?.data?.find((conversation) =>
                    conversation?.messages?.data?.find((message) => message.id === platformMessage.id)
                );
            }
        } catch (error) {
            logger.warn('[FB_WEBHOOK][ERROR_FETCHING_CONVERSATION] Error while fetching conversation', {
                platformMessage,
                pageId,
                customerId,
                error,
            });
        }
        if (!platformConversation) {
            logger.warn('[FB_WEBHOOK][COULD_NOT_FIND_CONVERSATION]', { platformMessage, pageId, customerId });
            return 'ok';
        }
        const promises = platformConversation.participants?.data?.map(async (participant: IgUser | FbUser, index: number) => {
            try {
                const picture = await fbCredentialsUseCases.getUserProfilePicture(
                    participant.id,
                    credentialId,
                    pageId,
                    isFacebookPage ? ConversationPlatform.MESSENGER : ConversationPlatform.INSTAGRAM
                );

                if (isFacebookPage) {
                    (platformConversation.participants.data[index] as FbUser).profile_pic = picture;
                } else {
                    (platformConversation.participants.data[index] as IgUser).profile_picture_url = picture;
                }
            } catch (error) {
                logger.warn('[FB_WEBHOOK][ERROR_FETCHING_USER_PROFILE] Error while fetching user profile', { participant, pageId, error });
                return;
            }
        });
        await Promise.all(promises);
        const conversationMapper = isFacebookPage ? new FacebookConversationMapper() : new InstagramConversationMapper();
        const platformKey = isFacebookPage ? PlatformKey.FACEBOOK : PlatformKey.INSTAGRAM;
        try {
            await this.triggerMessageReceived(
                platformKey,
                pageId,
                dataType,
                platformConversation,
                conversationMapper,
                platformMessage,
                webhookMessageData
            );
        } catch (error) {
            logger.error('[FB_WEBHOOK][MESSAGE_TRIGGERED_ERROR] Error while triggering message', {
                platformMessage,
                pageId,
                customerId,
                error,
            });
        }

        return 'ok';
    };

    getMappedMessage = ({
        conversationMapper,
        platform,
        platformMessage,
        externalUser,
        socialConversationId,
    }: {
        conversationMapper: FacebookConversationMapper | InstagramConversationMapper;
        platform: IPlatform;
        platformMessage: FbMessage | IgMessage;
        externalUser: FbUser | IgUser;
        socialConversationId?: string;
    }): IMessage => {
        if (conversationMapper instanceof FacebookConversationMapper) {
            return conversationMapper.mapToMalouMessage({
                message: platformMessage as FbMessage,
                platform,
                externalUserProfilePictureUrl: (externalUser as FbUser)?.profile_pic,
            });
        } else {
            return conversationMapper.mapToMalouMessage({
                message: platformMessage as IgMessage,
                platform,
                externalUserProfilePictureUrl: (externalUser as IgUser).profile_picture_url,
                socialConversationId,
            });
        }
    };

    getSocialConversationId = ({
        platformConversation,
        platformMessage,
    }: {
        platformConversation:
            | SocialConversationWithMessages<ConversationPlatform.INSTAGRAM>
            | SocialConversationWithMessages<ConversationPlatform.MESSENGER>;
        platformMessage: FbMessage | IgMessage;
    }): string => {
        if (isFbMessage(platformMessage)) {
            return platformMessage.thread_id || platformConversation.id;
        }
        return platformConversation.id;
    };

    getMappedConversation = ({
        conversationMapper,
        platformConversation,
        platform,
    }: {
        conversationMapper: FacebookConversationMapper | InstagramConversationMapper;
        platformConversation:
            | SocialConversationWithMessages<ConversationPlatform.INSTAGRAM>
            | SocialConversationWithMessages<ConversationPlatform.MESSENGER>;
        platform: IPlatform;
    }): IConversation => {
        if (conversationMapper instanceof FacebookConversationMapper) {
            return conversationMapper.mapToMalouConversation({
                conversation: platformConversation as SocialConversationWithMessages<ConversationPlatform.MESSENGER>,
                platform,
            });
        } else {
            return conversationMapper.mapToMalouConversation({
                conversation: platformConversation as SocialConversationWithMessages<ConversationPlatform.INSTAGRAM>,
                platform,
            });
        }
    };

    triggerMessageReceived = async (
        platformKey: PlatformKey,
        socialId: string,
        webhookType = 'message',
        platformConversation:
            | SocialConversationWithMessages<ConversationPlatform.INSTAGRAM>
            | SocialConversationWithMessages<ConversationPlatform.MESSENGER>,
        conversationMapper: FacebookConversationMapper | InstagramConversationMapper,
        platformMessage: IgMessage | FbMessage,
        webhookMessageData: MessagingMessageWebhook | MessagingReactionWebhook
    ) => {
        try {
            const platforms = await this._platformsRepository.find({
                filter: {
                    key: platformKey,
                    socialId,
                },
                options: {
                    lean: true,
                    populate: [
                        {
                            path: 'restaurant',
                        },
                    ],
                },
            });

            for (const platform of platforms) {
                const externalUser =
                    platformConversation?.participants?.data?.[0]?.id === platform.socialId
                        ? platformConversation?.participants?.data?.[1]
                        : platformConversation?.participants?.data?.[0];
                if (
                    platformKey === PlatformKey.INSTAGRAM &&
                    isMessage(webhookMessageData) &&
                    webhookMessageData?.message?.attachments?.some((attachment) => attachment.type === MetaAttachmentTypes.AUDIO)
                ) {
                    platformMessage.attachments = { data: [] };
                    platformMessage.attachments.data = webhookMessageData.message.attachments.map((attachment) => ({
                        audio: {
                            url: attachment.payload.url,
                            name: 'no_name',
                        },
                    }));
                }
                const socialConversationId = this.getSocialConversationId({ platformConversation, platformMessage });
                const mappedMessage = this.getMappedMessage({
                    conversationMapper,
                    platform,
                    platformMessage,
                    externalUser,
                    socialConversationId,
                });

                const mappedConversation = this.getMappedConversation({ conversationMapper, platformConversation, platform });

                const customerId = mappedConversation.userInfo.userSocialId;

                if (!customerId) {
                    logger.warn('[FB_WEBHOOK][NO_CUSTOMER_ID] No customer id found. Should not happen', {
                        platformConversation,
                        platformMessage,
                        externalUser,
                        socialConversationId,
                    });
                    return;
                }

                const newConversationStatus = mappedMessage.isFromRestaurant ? ConversationStatus.READ : ConversationStatus.UNREAD;
                const latestMessageAt = mappedMessage.socialCreatedAt;

                const upsertedConversation = await this._processUpsertConversation({
                    customerId,
                    platformKey,
                    restaurantId: platform.restaurantId,
                    conversation: mappedConversation,
                    newConversationStatus,
                    latestMessageAt,
                });

                const messageFindFilter = {
                    socialMessageId: platformMessage.id,
                    key: upsertedConversation.key,
                    conversationId: upsertedConversation._id,
                };

                const updatedMessage = await this._messagesRepository.upsert({
                    filter: messageFindFilter,
                    update: {
                        ...mappedMessage,
                        conversationId: upsertedConversation._id,
                    },
                    options: { lean: true },
                });

                if (webhookType === 'reaction') {
                    pusher
                        .trigger(`messaging-restaurant-${platform.restaurantId}`, 'reaction-received', {
                            message: updatedMessage,
                            conversation: upsertedConversation,
                        })
                        .catch((e) => logger.error('[PUSHER_ERROR]', e));
                } else {
                    logger.info('[SENDING_PUSHER_NOTIF]', {
                        messagingRestaurantId: `messaging-restaurant-${platform.restaurantId}`,
                        messageId: updatedMessage._id,
                        conversationId: upsertedConversation._id,
                    });
                    pusher
                        .trigger(`messaging-restaurant-${platform.restaurantId}`, 'message-received', {
                            message: updatedMessage,
                            conversation: upsertedConversation,
                        })
                        .catch((e) => logger.error('[PUSHER_ERROR]', e));

                    if (!updatedMessage.isFromRestaurant) {
                        await this._createMessageNotificationProducer.sendMessage({
                            messageId: updatedMessage._id.toString(),
                        });
                    }
                }
            }
        } catch (err) {
            logger.error('[WEBHOOK_TRIGGER_FB_MESSAGE_ERROR]', err);
            throw err;
        }
    };

    triggerMessageDeleted = async (messageId, isFacebookPage) => {
        const messagesToUpdate = await this._messagesRepository.find({
            filter: {
                socialMessageId: messageId,
                key: isFacebookPage ? PlatformKey.FACEBOOK : PlatformKey.INSTAGRAM,
            },
            projection: { _id: 1 },
        });
        const messagesUpdated = await Promise.all(
            messagesToUpdate?.map((message) =>
                this._messagesRepository.upsert({
                    filter: { _id: message._id },
                    update: {
                        isDeleted: true,
                        text: null,
                        story: null,
                        attachments: null,
                        isUnsupportedAttachment: false,
                        reactions: null,
                    },
                })
            )
        );

        const conversations = await this._conversationsRepository.find({
            filter: { _id: { $in: messagesUpdated.map((m) => m.conversationId) } },
        });

        for (const messageUpdated of messagesUpdated) {
            const conversation = conversations.find((c) => c._id.toString() === messageUpdated.conversationId.toString());
            if (!conversation) {
                logger.error('[WEBHOOK_DELETE_MESSAGE_ERROR] Conversation not found', messageUpdated);
                continue;
            }
            pusher
                .trigger(`messaging-restaurant-${conversation.restaurantId}`, 'message-deleted', {
                    message: messageUpdated,
                    conversation,
                })
                .catch((e) => logger.error('[PUSHER_ERROR]', e));
        }
    };

    getPlatformCredentialIds = async (isFacebookPage: boolean, pageId: string): Promise<string[]> => {
        if (isFacebookPage) {
            const credentials = await this._facebookCredentialsRepository.getFbCredentialOrderedByPageAccessWorking(pageId);
            return credentials?.map((credential) => credential.id) ?? [];
        }
        const credentials = await this._facebookCredentialsRepository.find({
            filter: {
                key: PlatformKey.FACEBOOK,
                pageAccess: { $exists: true },
                'pageAccess.igPageId': pageId,
            },
            options: { lean: true, sort: { lastSeenWorking: -1 } },
        });
        return credentials.map((credential) => credential._id.toString());
    };

    _processUpsertConversation = async ({
        customerId,
        platformKey,
        restaurantId,
        conversation,
        newConversationStatus,
        latestMessageAt,
    }: {
        customerId: string;
        platformKey: PlatformKey;
        restaurantId: DbId;
        conversation: IConversation;
        newConversationStatus: ConversationStatus;
        latestMessageAt: Date;
    }): Promise<IConversation> => {
        const updateConversationData = {
            ...conversation,
            status: newConversationStatus,
            latestMessageAt,
        };
        const upsertedConversation = await this._conversationsRepository.upsert({
            filter: {
                key: platformKey,
                restaurantId,
                'userInfo.userSocialId': customerId,
            },
            update: updateConversationData,
            options: { lean: true },
        });
        return upsertedConversation;
    };
}
