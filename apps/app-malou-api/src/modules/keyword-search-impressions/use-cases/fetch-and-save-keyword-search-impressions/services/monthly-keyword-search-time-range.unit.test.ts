import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { MAX_GMB_API_FETCH_MONTHS, MonthAndYear } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getManyKeywordSearchImpressions } from ':modules/keyword-search-impressions/tests/keyword-search-impressions.builder';
import { MonthlyKeywordSearchTimeRangeService } from ':modules/keyword-search-impressions/use-cases/fetch-and-save-keyword-search-impressions/services/monthly-keyword-search-time-range.service';

describe('MonthlyKeywordSearchTimeRangesService', () => {
    beforeAll(() => {
        registerRepositories(['KeywordSearchImpressionsRepository']);
    });

    describe.only('getTimeRangeForMonthlySaveData', () => {
        const service = container.resolve(MonthlyKeywordSearchTimeRangeService);

        // Helper function to mock current date
        const mockCurrentDate = (day: number, month: number, year: number) => {
            const originalNow = DateTime.now;
            (DateTime as any).now = () => DateTime.fromObject({ day, month, year });
            return () => {
                (DateTime as any).now = originalNow;
            };
        };

        afterEach(() => {
            // Restore DateTime.now after each test
            jest.restoreAllMocks();
        });

        it('should return months from the last 18 months when current day is the 5th of the month', async () => {
            // Mock current date to be 2025-03-05 (the 5th day)
            const restoreMock = mockCurrentDate(5, 3, 2025);
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult() {
                    const now = DateTime.fromObject({ day: 5, month: 3, year: 2025 });
                    const months: MonthAndYear[] = [];
                    // Should check last 18 months when day is 5th
                    for (let i = MAX_GMB_API_FETCH_MONTHS; i >= 1; i--) {
                        const date = now.minus({ months: i });
                        months.push({
                            month: date.month,
                            year: date.year,
                        });
                    }
                    return months;
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });
            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });

        it('should return months from the last 6 months when current day is not the 5th of the month', async () => {
            // Mock current date to be 2025-03-10 (not the 5th day)
            const restoreMock = mockCurrentDate(10, 3, 2025);
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult() {
                    const now = DateTime.fromObject({ day: 10, month: 3, year: 2025 });
                    const months: MonthAndYear[] = [];
                    // Should check last 6 months when day is not 5th
                    for (let i = 6; i >= 1; i--) {
                        const date = now.minus({ months: i });
                        months.push({
                            month: date.month,
                            year: date.year,
                        });
                    }
                    return months;
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });

            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });

        it('should return only missing months when some data already exists (5th of month)', async () => {
            // Mock current date to be 2025-03-05
            const restoreMock = mockCurrentDate(5, 3, 2025);
            const now = DateTime.fromObject({ day: 5, month: 3, year: 2025 });
            const restaurantId = newDbId();

            // Create data for some months, leaving gaps
            const existingDataStartDate = now.minus({ months: 18 }).toJSDate();
            const existingDataEndDate = now.minus({ months: 15 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return getManyKeywordSearchImpressions(restaurantId, existingDataStartDate, existingDataEndDate);
                        },
                    },
                },
                expectedResult() {
                    const months: MonthAndYear[] = [];
                    // Should return missing months from the last 18 months
                    for (let i = 14; i >= 1; i--) {
                        const date = now.minus({ months: i });
                        months.push({
                            month: date.month,
                            year: date.year,
                        });
                    }
                    return months;
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });

            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });

        it('should return only missing months when some data already exists (not 5th of month)', async () => {
            // Mock current date to be 2025-03-10
            const restoreMock = mockCurrentDate(10, 3, 2025);
            const now = DateTime.fromObject({ day: 10, month: 3, year: 2025 });
            const restaurantId = newDbId();

            // Create data for some months, leaving gaps
            const existingDataStartDate = now.minus({ months: 6 }).toJSDate();
            const existingDataEndDate = now.minus({ months: 4 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return getManyKeywordSearchImpressions(restaurantId, existingDataStartDate, existingDataEndDate);
                        },
                    },
                },
                expectedResult() {
                    const months: MonthAndYear[] = [];
                    // Should return missing months from the last 6 months
                    for (let i = 3; i >= 1; i--) {
                        const date = now.minus({ months: i });
                        months.push({
                            month: date.month,
                            year: date.year,
                        });
                    }
                    return months;
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });

            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });

        it('should return empty array when all data exists for the required period (5th of month)', async () => {
            // Mock current date to be 2025-03-05
            const restoreMock = mockCurrentDate(5, 3, 2025);
            const now = DateTime.fromObject({ day: 5, month: 3, year: 2025 });
            const restaurantId = newDbId();

            // Create data for all months in the last 18 months
            const startDate = now.minus({ months: MAX_GMB_API_FETCH_MONTHS }).toJSDate();
            const endDate = now.minus({ months: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return getManyKeywordSearchImpressions(restaurantId, startDate, endDate);
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });

            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });

        it('should return empty array when all data exists for the required period (not 5th of month)', async () => {
            // Mock current date to be 2025-03-15
            const restoreMock = mockCurrentDate(15, 3, 2025);
            const now = DateTime.fromObject({ day: 15, month: 3, year: 2025 });
            const restaurantId = newDbId();

            // Create data for all months in the last 6 months
            const startDate = now.minus({ months: 6 }).toJSDate();
            const endDate = now.minus({ months: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return getManyKeywordSearchImpressions(restaurantId, startDate, endDate);
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });

            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });

        it('should handle edge case when current date is exactly the threshold day (5th)', async () => {
            // Mock current date to be 2025-03-05
            const restoreMock = mockCurrentDate(5, 3, 2025);
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult() {
                    const now = DateTime.fromObject({ day: 5, month: 3, year: 2025 });
                    const months: MonthAndYear[] = [];
                    // Should use MAX_GMB_API_FETCH_MONTHS (18) when day is exactly 5
                    for (let i = MAX_GMB_API_FETCH_MONTHS; i >= 1; i--) {
                        const date = now.minus({ months: i });
                        months.push({
                            month: date.month,
                            year: date.year,
                        });
                    }
                    return months;
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });

            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });

        it('should handle scattered missing months in the required period', async () => {
            // Mock current date to be 2025-03-05
            const restoreMock = mockCurrentDate(5, 3, 2025);
            const now = DateTime.fromObject({ day: 5, month: 3, year: 2025 });
            const restaurantId = newDbId();

            // Create data with gaps - months 18-15 and 5-2 have data, leaving gaps in between
            const firstPeriodStart = now.minus({ months: 18 }).toJSDate();
            const firstPeriodEnd = now.minus({ months: 15 }).toJSDate();
            const secondPeriodStart = now.minus({ months: 5 }).toJSDate();
            const secondPeriodEnd = now.minus({ months: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return [
                                ...getManyKeywordSearchImpressions(restaurantId, firstPeriodStart, firstPeriodEnd),
                                ...getManyKeywordSearchImpressions(restaurantId, secondPeriodStart, secondPeriodEnd),
                            ];
                        },
                    },
                },
                expectedResult() {
                    const months: MonthAndYear[] = [];
                    // Should return missing months: 14-6 and 1
                    for (let i = 14; i >= 6; i--) {
                        const date = now.minus({ months: i });
                        months.push({
                            month: date.month,
                            year: date.year,
                        });
                    }
                    // Add the previous month (1 month ago)
                    const previousMonth = now.minus({ months: 1 });
                    months.push({
                        month: previousMonth.month,
                        year: previousMonth.year,
                    });
                    return months;
                },
            });

            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await service.getTimeRangeForMonthlySaveData({ restaurantId: restaurantId.toString() });

            expect(result).toIncludeSameMembers(expectedResult);
            restoreMock();
        });
    });

    describe('isPreviousMonthAndYear', () => {
        const service = container.resolve(MonthlyKeywordSearchTimeRangeService);

        it('should return true if the month and year correspond to the previous month', () => {
            const now = DateTime.now();
            const previousMonth = now.minus({ months: 1 });

            const result = service.isPreviousMonthAndYear({ month: previousMonth.month, year: previousMonth.year });
            expect(result).toBe(true);
        });

        it('should return false if the month and year do not correspond to the previous month', () => {
            const now = DateTime.now();
            const twoMonthsAgo = now.minus({ months: 2 });

            const result = service.isPreviousMonthAndYear({ month: twoMonthsAgo.month, year: twoMonthsAgo.year });
            expect(result).toBe(false);
        });
    });
});
