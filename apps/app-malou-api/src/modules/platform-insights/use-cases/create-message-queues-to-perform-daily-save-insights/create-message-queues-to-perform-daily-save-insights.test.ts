import { container } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { CreateMessageQueuesToPerformDailySaveInsightsUseCase } from ':modules/platform-insights/use-cases/create-message-queues-to-perform-daily-save-insights/create-message-queues-to-perform-daily-save-insights.use-case';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

const createMessageQueuesToPerformDailySaveInsightsUseCase = container.resolve(CreateMessageQueuesToPerformDailySaveInsightsUseCase);

describe('CreateMessageQueuesToPerformDailySaveInsightsUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PlatformsRepository', 'RestaurantsRepository']);
        createMessageQueuesToPerformDailySaveInsightsUseCase.initialize();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should create 2 message queues from 4 platforms by filtering out DELIVEROO', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().active(true).build(),
                            getDefaultRestaurant().active(true).build(),
                            getDefaultRestaurant().active(true).build(),
                            getDefaultRestaurant().active(true).build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.INSTAGRAM).build(),
                            getDefaultPlatform().restaurantId(dependencies.restaurants()[1]._id).key(PlatformKey.GMB).build(),
                            getDefaultPlatform().restaurantId(dependencies.restaurants()[2]._id).key(PlatformKey.FACEBOOK).build(),
                            getDefaultPlatform().restaurantId(dependencies.restaurants()[3]._id).key(PlatformKey.DELIVEROO).build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        const spyonSendMessage = jest.spyOn(createMessageQueuesToPerformDailySaveInsightsUseCase, 'sendMessage');

        await testCase.build();

        await createMessageQueuesToPerformDailySaveInsightsUseCase.execute();

        expect(spyonSendMessage).toHaveBeenCalledTimes(3);
    });
    it('should filter out inactive restaurants', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().active(true).build(),
                            getDefaultRestaurant().active(false).build(),
                            getDefaultRestaurant().active(true).build(),
                        ];
                    },
                },
                platforms: {
                    data(dependencies) {
                        return [
                            getDefaultPlatform().restaurantId(dependencies.restaurants()[0]._id).key(PlatformKey.INSTAGRAM).build(),
                            getDefaultPlatform().restaurantId(dependencies.restaurants()[1]._id).key(PlatformKey.GMB).build(),
                            getDefaultPlatform().restaurantId(dependencies.restaurants()[2]._id).key(PlatformKey.FACEBOOK).build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        const spyonSendMessage = jest.spyOn(createMessageQueuesToPerformDailySaveInsightsUseCase, 'sendMessage');

        await testCase.build();

        await createMessageQueuesToPerformDailySaveInsightsUseCase.execute();

        expect(spyonSendMessage).toHaveBeenCalledTimes(2);
    });
});
