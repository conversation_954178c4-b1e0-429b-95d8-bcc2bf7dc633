import { isNil } from 'lodash';
import assert from 'node:assert/strict';
import { autoInjectable } from 'tsyringe';

import { ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    errorReplacer,
    isNotNil,
    MalouErrorCode,
    MalouMetric,
    PlatformKey,
    StoredInDBInsightsMetric,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { InsightsAggregator } from ':modules/platform-insights/platform-insights.aggregators';
import { platformRatingsFetchCounter } from ':modules/platform-insights/platform-insights.metrics';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import {
    DailyValue,
    MetricToDataValues,
    PlatformInsightUseCase,
    RatingValue,
    TimeScaleToMetricToDataValues,
} from ':modules/platform-insights/platform-insights.types';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { DeliverooProvider } from ':modules/providers/platforms/deliveroo/deliveroo.provider';

@autoInjectable()
export class DeliverooPlatformInsights implements PlatformInsightUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _deliverooProvider: DeliverooProvider,
        private readonly _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    async fetchTodayRating(restaurantId: string): Promise<RatingValue | undefined> {
        try {
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId: toDbId(restaurantId), key: PlatformKey.DELIVEROO },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
            });
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: {
                        restaurantId,
                        platformKey: PlatformKey.DELIVEROO,
                    },
                });
            }
            assert(platform.drnId, '[DELIVEROO_PLATFORM_INSIGHTS] drnId is not defined on platform');

            const credentialId = platform.credentials?.[0];
            assert(credentialId, '[DELIVEROO_PLATFORM_INSIGHTS] credentialId is not defined on platform');

            const result = await this._deliverooProvider.getPlatformRating({
                credentialId: credentialId.toString(),
                drnId: platform.drnId,
            });

            // if result is null or undefined, it means there was an issue fetching the rating via the lambda
            if (isNil(result)) {
                throw new MalouError(MalouErrorCode.FAILED_TO_FETCH_PLATFORM_RATING, {
                    metadata: {
                        restaurantId,
                        platformKey: PlatformKey.DELIVEROO,
                    },
                });
            }

            platformRatingsFetchCounter.add(1, {
                source: PlatformKey.DELIVEROO,
                status: 'success',
            });

            return result;
        } catch (err) {
            platformRatingsFetchCounter.add(1, {
                source: PlatformKey.DELIVEROO,
                status: 'failure',
            });
            logger.warn('[DeliverooPlatformInsights] fetchTodayRating', {
                restaurantId,
                error: err,
            });
            throw err;
        }
    }

    getInsightsAggregated = async (
        restaurantId: string,
        metrics: MalouMetric[],
        aggregators: AggregationTimeScale[],
        filters
    ): Promise<TimeScaleToMetricToDataValues> => {
        let platform;
        try {
            platform = await this._platformsRepository.findOne({
                filter: { restaurantId, key: PlatformKey.DELIVEROO },
                options: { lean: true },
            });
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: { restaurantId, platformKey: PlatformKey.DELIVEROO },
                });
            }
        } catch (err) {
            logger.warn('[DeliverooPlatformInsights] getInsightsAggregated', err);
            return { error: true, message: JSON.stringify(err, errorReplacer) };
        }

        const { startDate, endDate } = filters;
        const insightsByDay: MetricToDataValues<DailyValue> = await this._getInsightsByDay(platform.socialId, metrics, startDate, endDate);
        if ('error' in insightsByDay) {
            return insightsByDay;
        }
        return new InsightsAggregator().aggregateInsights(insightsByDay, aggregators, startDate, endDate);
    };

    private _getInsightsByDay = async (
        pageId: string,
        metrics: MalouMetric[],
        startDate: Date,
        endDate: Date
    ): Promise<MetricToDataValues<DailyValue>> => {
        const insightsByDay: MetricToDataValues<DailyValue> = {};

        if (metrics.includes(MalouMetric.PLATFORM_RATING)) {
            insightsByDay[MalouMetric.PLATFORM_RATING] = await this._getPlatformRatingByDay(pageId, startDate, endDate);
        }

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }

        return insightsByDay;
    };

    private _getPlatformRatingByDay = async (pageId: string, startDate: Date, endDate: Date): Promise<DailyValue[]> => {
        const ratingInsights = await this._platformInsightsRepository.find({
            filter: {
                metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                socialId: pageId,
                platformKey: PlatformKey.DELIVEROO,
                date: { $gte: startDate, $lte: endDate },
            },
            options: { sort: { date: -1 }, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });

        return ratingInsights
            .map((insight) => (insight.value ? { date: new Date(insight.year, insight.month, insight.day), value: insight.value } : null))
            .filter(isNotNil);
    };

    insertTodayFollowers(): Promise<void> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'insertTodayFollowers Method not implemented',
            metadata: {
                platform: PlatformKey.DELIVEROO,
            },
        });
    }
}
