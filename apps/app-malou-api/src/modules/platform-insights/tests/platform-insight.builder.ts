import { Builder } from 'builder-pattern';

import { IPlatformInsight, newDbId } from '@malou-io/package-models';
import { PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

type PlatformInsightPayload = IPlatformInsight;

const _buildPlatformInsight = (cred: PlatformInsightPayload) => Builder<PlatformInsightPayload>(cred);

export const getDefaultPlatformInsight = () =>
    _buildPlatformInsight({
        _id: newDbId(),
        platformKey: PlatformKey.FACEBOOK,
        createdAt: new Date(),
        date: new Date(),
        day: new Date().getDate(),
        metric: StoredInDBInsightsMetric.FOLLOWERS,
        value: 1,
        platformId: newDbId(),
        month: new Date().getMonth(),
        socialId: 'socialId',
        updatedAt: new Date(),
        year: new Date().getFullYear(),
    });
