/* Task to simulate platform creation by chaining upsert and pullOverview calls
 * This task simulates the behavior found in update-account-managed-platform.service.ts */
import 'reflect-metadata';

import ':env';

import ':di';
import { NextFunction } from 'express';
import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import PlatformsController from ':modules/platforms/platforms.controller';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import ':plugins/db';

interface MockRequest<TParams = any, TQuery = any, TBody = any> {
    params: TParams;
    query: TQuery;
    body: TBody;
    user: any;
    userRestaurantsAbility: any;
}

interface MockResponse {
    status: (code: number) => MockResponse;
    json: (data: any) => MockResponse;
}

@singleton()
class SimulatePlatformCreationTask {
    constructor(
        private readonly _platformsController: PlatformsController,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute() {
        try {
            // Configuration de la tâche - À modifier selon vos besoins
            const testConfig = {
                restaurantId: '651444606d6bef2863015fbc', // Remplacer par un vrai ID de restaurant
                platformKey: PlatformKey.LAFOURCHETTE, // Plateforme à tester
                socialId: '850298', // ID social de test
                userId: '5ee9f46f432884bad0b9934d', // Remplacer par un vrai ID utilisateur
            };

            console.log('🚀 Starting platform creation simulation...');
            console.log('Configuration:', testConfig);

            // Vérifier que le restaurant existe
            const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: testConfig.restaurantId } });
            if (!restaurant) {
                throw new Error(`Restaurant with ID ${testConfig.restaurantId} not found`);
            }
            console.log('✅ Restaurant found:', restaurant.name);

            // Vérifier que l'utilisateur existe
            const user = await this._usersRepository.findById(testConfig.userId);
            if (!user) {
                throw new Error(`User with ID ${testConfig.userId} not found`);
            }
            console.log('✅ User found:', user.email);

            // Créer un mock d'ability CASL (permissions)
            const mockAbility = {
                can: () => true, // Permet toutes les actions pour le test
            };

            // Mock de la réponse HTTP
            const mockResponse: MockResponse = {
                status: (code: number) => {
                    console.log(`📤 Response status: ${code}`);
                    return mockResponse;
                },
                json: (data: any) => {
                    console.log('📤 Response data:', JSON.stringify(data, null, 2));
                    return mockResponse;
                },
            };

            // Mock de next function
            const mockNext: NextFunction = (error?: any) => {
                if (error) {
                    console.error('❌ Error in next function:', error);
                    throw error;
                }
            };

            // ÉTAPE 1: Simuler l'appel upsert
            console.log('\n🔄 Step 1: Executing upsert...');

            // Le body doit correspondre au format attendu par le validateur (snake_case)
            const upsertBody = {
                restaurant_id: testConfig.restaurantId,
                platform: {
                    key: testConfig.platformKey,
                    socialId: testConfig.socialId,
                },
                credentialId: undefined, // Optional credential ID
            };

            const upsertRequest: MockRequest<any, any, any> = {
                params: {},
                query: {},
                body: upsertBody,
                user: {
                    _id: toDbId(testConfig.userId),
                    email: user.email,
                },
                userRestaurantsAbility: mockAbility,
            };

            await this._platformsController.handleUpsertPlatform(upsertRequest as any, mockResponse as any, mockNext);

            console.log('✅ Upsert completed successfully');

            // Attendre un peu entre les appels (comme dans le service frontend)
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // ÉTAPE 2: Simuler l'appel pullOverview avec switch_platform = true
            console.log('\n🔄 Step 2: Executing pullOverview with switch_platform=true...');

            const pullOverviewRequest: MockRequest<any, any> = {
                params: {
                    restaurant_id: testConfig.restaurantId,
                    platform_key: testConfig.platformKey,
                },
                query: {
                    switch_platform: 'true',
                },
                body: {},
                user: {
                    _id: toDbId(testConfig.userId),
                    email: user.email,
                },
                userRestaurantsAbility: mockAbility,
            };

            await this._platformsController.handlePullOverviewForPlatform(pullOverviewRequest as any, mockResponse as any, mockNext);

            console.log('✅ PullOverview completed successfully');

            // Vérifier le résultat final
            const finalPlatform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
                testConfig.restaurantId,
                testConfig.platformKey
            );

            if (finalPlatform) {
                console.log('\n🎉 Platform creation simulation completed successfully!');
                console.log('Final platform state:', {
                    id: finalPlatform._id,
                    key: finalPlatform.key,
                    socialId: finalPlatform.socialId,
                    restaurantId: finalPlatform.restaurantId,
                });
            } else {
                console.log('⚠️ Platform not found after creation - this might be expected for some platforms');
            }
        } catch (error: any) {
            console.error('❌ Error during platform creation simulation:', error.message);
            console.error('Stack trace:', error.stack);
            throw error;
        }
    }
}

const task = container.resolve(SimulatePlatformCreationTask);
task.execute()
    .then(() => {
        console.log('\n✅ Task completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Task failed:', error);
        process.exit(1);
    });
